from dahua_rpc_grok import DahuaRPC

# Example of logging in to a Dahua NVR
username = "admin"
password = "!?@#qwer"

dahua = DahuaRPC(host="**************", username=username, password=password)
print(dahua.login())  # Should return True if successful
print(dahua.current_time())  # Should return the current time on the NVR

# Get logs for a specific date range and save to CSV
import csv

logs = dahua.get_logs(start_time="2025-07-08 00:00:00", end_time="2025-07-08 23:59:59", token=67108866)
with open("nvr_logs.csv", "w", newline="") as f:
    writer = csv.writer(f)
    writer.writerow(["User", "Time", "Type", "Detail"])
    for log in logs.get("params", {}).get("items", []):
        detail = log.get("Detail", "")
        detail = detail.replace('\n', ' ') if detail else ""
        writer.writerow([log.get("User"), log.get("Time"), log.get("Type"), detail])